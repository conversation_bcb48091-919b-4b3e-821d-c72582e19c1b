import type { APIRoute } from 'astro';
import { 
  getPaymentTransactionByPaymentId, 
  updatePaymentTransaction, 
  updateOrderPaymentStatus,
  updateOrderStatus
} from '../../../db/database';

export const prerender = false;

// PhonePe API configuration (use environment variables in production)
const SALT_KEY = "099eb0cd-02cf-4e2a-8aca-3e6c6aff0399";
const SALT_INDEX = "1";

/**
 * Webhook handler for PhonePe payment notifications
 */
export const POST: APIRoute = async ({ request, locals }) => {
  try {
    // Verify request signature in X-VERIFY header
    const signature = request.headers.get('X-VERIFY');
    
    if (!signature) {
      console.error('Missing signature in webhook request');
      return new Response(JSON.stringify({ 
        success: false, 
        message: 'Invalid request: missing signature' 
      }), {
        status: 400,
        headers: { 'Content-Type': 'application/json' }
      });
    }
    
    // Get the raw request body for signature verification
    const rawBody = await request.text();
    let webhookData;
    
    try {
      webhookData = JSON.parse(rawBody);
    } catch (e) {
      return new Response(JSON.stringify({ 
        success: false, 
        message: 'Invalid request body: not valid JSON' 
      }), {
        status: 400,
        headers: { 'Content-Type': 'application/json' }
      });
    }
    
    // Verify signature for production security
    try {
      const crypto = await import('crypto');
      const calculatedSignature = `${crypto.createHash('sha256').update(rawBody + SALT_KEY).digest('hex')}###${SALT_INDEX}`;
      
      // In development mode, log signatures for debugging
      if (process.env.NODE_ENV === 'development') {
        console.log('Webhook signature verification:');
        console.log('Received signature:', signature);
        console.log('Calculated signature:', calculatedSignature);
      }
      
      // For production, always verify signatures
      if (process.env.NODE_ENV === 'production' && signature !== calculatedSignature) {
        console.error('Invalid signature in webhook request');
        return new Response(JSON.stringify({ 
          success: false, 
          message: 'Invalid signature' 
        }), {
          status: 401,
          headers: { 'Content-Type': 'application/json' }
        });
      }
    } catch (cryptoError) {
      console.error('Error verifying webhook signature:', cryptoError);
      // In case of crypto error, log but don't fail in development
      if (process.env.NODE_ENV === 'production') {
        return new Response(JSON.stringify({ 
          success: false, 
          message: 'Signature verification failed' 
        }), {
          status: 500,
          headers: { 'Content-Type': 'application/json' }
        });
      }
    }
    
    // Extract transaction details
    const { merchantTransactionId, transactionId, amount, paymentState } = webhookData;
    
    if (!merchantTransactionId) {
      return new Response(JSON.stringify({ 
        success: false, 
        message: 'Invalid request: missing merchantTransactionId' 
      }), {
        status: 400,
        headers: { 'Content-Type': 'application/json' }
      });
    }
    
    // Retrieve our stored transaction record
    const transaction = await getPaymentTransactionByPaymentId(
      locals.runtime.env, 
      merchantTransactionId
    );
    
    if (!transaction) {
      return new Response(JSON.stringify({ 
        success: false, 
        message: 'Transaction not found' 
      }), {
        status: 404,
        headers: { 'Content-Type': 'application/json' }
      });
    }
    
    // Map PhonePe payment state to our status values
    let newStatus = '';
    let orderPaymentStatus = '';
    let orderStatus = '';
    
    switch (paymentState?.toLowerCase()) {
      case 'completed':
      case 'success':
        newStatus = 'success';
        orderPaymentStatus = 'paid';
        orderStatus = 'processing'; // Move to processing after successful payment
        break;
      case 'failed':
        newStatus = 'failed';
        orderPaymentStatus = 'failed';
        break;
      case 'pending':
        newStatus = 'pending';
        orderPaymentStatus = 'pending';
        break;
      case 'cancelled':
        newStatus = 'cancelled';
        orderPaymentStatus = 'cancelled';
        orderStatus = 'cancelled';
        break;
      default:
        newStatus = 'unknown';
        orderPaymentStatus = 'pending';
    }
    
    // Update transaction status in our database
    await updatePaymentTransaction(locals.runtime.env, merchantTransactionId, {
      status: newStatus,
      gateway_response: rawBody // Store the complete webhook payload
    });
    
    // Update order payment status
    await updateOrderPaymentStatus(locals.runtime.env, transaction.order_id, orderPaymentStatus);
    
    // If payment was successful, update order status to processing
    if (orderStatus === 'processing') {
      await updateOrderStatus(locals.runtime.env, transaction.order_id, 'processing');
    }
    
    // If payment failed or was cancelled, update order status
    if (orderStatus === 'cancelled') {
      await updateOrderStatus(locals.runtime.env, transaction.order_id, 'cancelled');
    }
    
    // Send success response back to PhonePe
    return new Response(JSON.stringify({ 
      success: true, 
      message: 'Webhook received and processed successfully',
      data: {
        merchantTransactionId,
        transactionId
      }
    }), {
      status: 200,
      headers: { 'Content-Type': 'application/json' }
    });
    
  } catch (error) {
    console.error('Error processing webhook:', error);
    return new Response(JSON.stringify({ 
      success: false, 
      message: 'Internal server error' 
    }), {
      status: 500,
      headers: { 'Content-Type': 'application/json' }
    });
  }
}