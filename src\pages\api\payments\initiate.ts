import type { APIRoute } from 'astro';
import { getOrderById, createPaymentTransaction, getOrderPaymentTransactions } from '../../../db/database';
import { authMiddleware } from '../../../middleware/auth';

export const prerender = false;

// PhonePe API configuration (use environment variables in production)
const PHONEPE_API_URL =   "https://api-preprod.phonepe.com/apis/pg-sandbox";
const MERCHANT_ID =   "TEST-M22TVSLFNFUG7_25071";
const SALT_KEY =  "099eb0cd-02cf-4e2a-8aca-3e6c6aff0399";
const SALT_INDEX =  "1";

/**
 * Initiate a payment for an order using PhonePe
 */
export const POST: APIRoute = async ({ request, locals }) => {
  try {
    // Authenticate the user
    const authResult = await authMiddleware({ request });
    if (authResult instanceof Response) {
      return authResult; // Return authentication error
    }
    
    const user = (authResult as any).user;
    
    // Parse request body
    const { order_id } = await request.json() as { order_id: number };
    
    if (!order_id) {
      return new Response(JSON.stringify({ 
        success: false, 
        message: "Order ID is required" 
      }), {
        status: 400,
        headers: { 'Content-Type': 'application/json' }
      });
    }
    
    // Verify order exists and belongs to the user
    const order = await getOrderById(locals.runtime.env, order_id, user.id);
    
    if (!order) {
      return new Response(JSON.stringify({ 
        success: false, 
        message: "Order not found" 
      }), {
        status: 404,
        headers: { 'Content-Type': 'application/json' }
      });
    }
    
    // Verify payment method
    if (order.payment_method !== 'online') {
      return new Response(JSON.stringify({ 
        success: false, 
        message: "This order doesn't require online payment" 
      }), {
        status: 400,
        headers: { 'Content-Type': 'application/json' }
      });
    }
    
    // Verify payment status (should be pending)
    if (order.payment_status !== 'pending') {
      return new Response(JSON.stringify({ 
        success: false, 
        message: `Payment already ${order.payment_status}` 
      }), {
        status: 400,
        headers: { 'Content-Type': 'application/json' }
      });
    }
    
    // Check for existing transactions
    const transactions = await getOrderPaymentTransactions(locals.runtime.env, order.id);
    const pendingTransaction = transactions.find(tx => 
      tx.status === 'initiated' || tx.status === 'pending');
    
    if (pendingTransaction) {
      // Return existing transaction details
      try {
        const gatewayResponse = JSON.parse(pendingTransaction.gateway_response || '{}');
        return new Response(JSON.stringify({ 
          success: true,
          transaction: pendingTransaction,
          redirectUrl: gatewayResponse.data?.instrumentResponse?.redirectInfo?.url || null,
          message: "Payment already initiated"
        }), {
          status: 200,
          headers: { 'Content-Type': 'application/json' }
        });
      } catch (e) {
        // Handle parse error
        console.error('Error parsing gateway response:', e);
      }
    }
    
    // Generate a unique merchant transaction ID
    const merchantTxnId = `TXN_${Date.now()}_${Math.floor(Math.random() * 1000000)}`;
    
    // Get phone number from address
    let phoneNumber = '';
    if (order.address && order.address.phone) {
      phoneNumber = order.address.phone.replace(/[^0-9]/g, '');
    }
    
    // Create PhonePe request payload
    const phonepePayload = {
      merchantId: MERCHANT_ID,
      merchantTransactionId: merchantTxnId,
      merchantUserId: `USER_${user.id}`,
      amount: Math.round(order.total_amount * 100), // Amount in paise (1 INR = 100 paise)
      redirectUrl: `${new URL(request.url).origin}/checkout/status?transactionId=${merchantTxnId}`,
      redirectMode: "REDIRECT",
      callbackUrl: `${new URL(request.url).origin}/api/payments/webhook`,
      mobileNumber: phoneNumber,
      paymentInstrument: {
        type: "PAY_PAGE"
      }
    };
    
    // Convert to base64
    const base64Payload = btoa(JSON.stringify(phonepePayload));
    
    // Generate checksum (SHA256 hash) for the payload
    // In production, use a proper crypto library
    const crypto = await import('crypto');
    const string = `${base64Payload}/pg/v1/pay${SALT_KEY}`;
    const sha256 = crypto.createHash('sha256').update(string).digest('hex');
    const checksum = `${sha256}###${SALT_INDEX}`;
    
    // Make API call to PhonePe
    const response = await fetch(`${PHONEPE_API_URL}/pg/v1/pay`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'X-VERIFY': checksum
      },
      body: JSON.stringify({ request: base64Payload })
    });
    
    const phonepeResponse: any = await response.json();
    
    // Check for API errors
    if (!phonepeResponse.success) {
      console.error('PhonePe API error:', phonepeResponse);
      return new Response(JSON.stringify({ 
        success: false, 
        message: phonepeResponse.message || "Payment gateway error" 
      }), {
        status: 500,
        headers: { 'Content-Type': 'application/json' }
      });
    }
    
    // Store transaction record in our database
    const transaction = await createPaymentTransaction(locals.runtime.env, {
      order_id: order.id,
      transaction_id: merchantTxnId,
      payment_method: 'phonepe',
      amount: order.total_amount,
      status: 'initiated',
      gateway_response: JSON.stringify(phonepeResponse)
    });
    
    if (!transaction) {
      return new Response(JSON.stringify({ 
        success: false, 
        message: "Failed to store transaction" 
      }), {
        status: 500,
        headers: { 'Content-Type': 'application/json' }
      });
    }
    
    // Return success with redirect URL for the payment page
    return new Response(JSON.stringify({ 
      success: true, 
      transaction: transaction,
      redirectUrl: phonepeResponse.data.instrumentResponse.redirectInfo.url,
      message: "Payment initiated successfully"
    }), {
      status: 200,
      headers: { 'Content-Type': 'application/json' }
    });
    
  } catch (error) {
    console.error('Error initiating payment:', error);
    return new Response(JSON.stringify({ 
      success: false, 
      message: "Failed to initiate payment" 
    }), {
      status: 500,
      headers: { 'Content-Type': 'application/json' }
    });
  }
};