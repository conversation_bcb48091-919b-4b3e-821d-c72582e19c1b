/**
 * For more details on how to configure Wrangler, refer to:
 * https://developers.cloudflare.com/workers/wrangler/configuration/
 */
{
	"$schema": "node_modules/wrangler/config-schema.json",
	"name": "sreekar-publications",
	"compatibility_date": "2025-04-30",
	"compatibility_flags": [
		"nodejs_compat"
	],
	"pages_build_output_dir": "./dist",
	"observability": {
		"enabled": true
	},
	/**
	 * Bindings
	 * Bindings allow your Worker to interact with resources on the Cloudflare Developer Platform, including
	 * databases, object storage, AI inference, real-time communication and more.
	 * https://developers.cloudflare.com/workers/runtime-apis/bindings/
	 */
	"d1_databases": [
		{
			"binding": "SNACKSWIFT_DB",
			"database_name": "sreekar-publications",
			"database_id": "012c3abd-869a-44d0-999f-70e8fcb3c34c"
		}
	],
	/**
	 * Smart Placement
	 * Docs: https://developers.cloudflare.com/workers/configuration/smart-placement/#smart-placement
	 */
	// "placement": { "mode": "smart" },
	/**
	 * Environment Variables
	 * https://developers.cloudflare.com/workers/wrangler/configuration/#environment-variables
	 */
	"vars": {
		"WHATSAPP_PHONE_NUMBER_ID": "646817298521704",
		"WHATSAPP_ACCESS_TOKEN": "EAAKcsSHpoIABO5WlCcwCMYYfNwbsWhPvJEwr2ZAXe1KmvZAJwhXV1epKWZBUZCamomGLRcghxHCbfFvZCusk22NB03la1xrZCK2SvNXZC2bSQRcc09QosCOQtMqC94Sf410wbNb5WukNOXDZBJNJBenZC8HbqnsGiVSPcew3Es9SJKmFkDfcFVnhbmN43bo2yVAZDZD",
		"PHONEPE_API_URL": "https://api-preprod.phonepe.com/apis/pg-sandbox",
		"PHONEPE_MERCHANT_ID":  "TEST-M22TVSLFNFUG7_25071",
		"PHONEPE_SALT_KEY": "099eb0cd-02cf-4e2a-8aca-3e6c6aff0399",
		"PHONEPE_SALT_INDEX": "1"
	},
	/**
	 * Note: Use secrets to store sensitive data.
	 * https://developers.cloudflare.com/workers/configuration/secrets/
	 */
	/**
	 * Static Assets
	 * https://developers.cloudflare.com/workers/static-assets/binding/
	 */
	// "assets": { "directory": "./public/", "binding": "ASSETS" },
	/**
	 * Service Bindings (communicate between multiple Workers)
	 * https://developers.cloudflare.com/workers/wrangler/configuration/#service-bindings
	 */
	// "services": [{ "binding": "MY_SERVICE", "service": "my-service" }]
}